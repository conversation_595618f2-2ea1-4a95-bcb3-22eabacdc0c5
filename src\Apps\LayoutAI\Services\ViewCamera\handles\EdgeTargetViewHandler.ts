import { BaseViewHandler } from "./BaseViewHandler";
import { ViewCameraRuler } from "../ViewCameraRuler";
import {
    TViewCameraEntity,
    IViewCameraGenerateOptions
} from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { RoomSpaceAreaType, IType2UITypeDict } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { ZEdge, ZRect } from "@layoutai/z_polygon";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { Vector3 } from "three";

/**
 * 根据房间中某个图元生成视角
 * 位置：分区的边中点
 */
export class EdgeTargetViewHandler extends BaseViewHandler {
    handle(
        ruler: ViewCameraRuler,
        roomEntity: TRoomEntity,
        options: IViewCameraGenerateOptions
    ): TViewCameraEntity[] {
        let entities: TViewCameraEntity[] = [];
        if (this.checkCondition(ruler, roomEntity)) {
            let targetFurnitureList = ruler.targets;
            let furnitureList = roomEntity.getFurnitureEntitiesOnFlat();
            // 找客餐厅分区
            let areas: TSubSpaceAreaEntity[] = roomEntity._sub_room_areas;
            let LivingArea: TSubSpaceAreaEntity = null; // 客厅
            let DiningArea: TSubSpaceAreaEntity = null; // 餐厅
            areas.forEach(area => {
                if (area.space_area_type === RoomSpaceAreaType.LivingArea) {
                    LivingArea = area;
                } else if (area.space_area_type === RoomSpaceAreaType.DiningArea) {
                    DiningArea = area;
                }
            });
            if (ruler.condition?.spaceArea) {
                let targetArea: TSubSpaceAreaEntity = null;
                let nearArea: TSubSpaceAreaEntity = null;
                if (ruler.condition.spaceArea === IType2UITypeDict[RoomSpaceAreaType.LivingArea]) {
                    targetArea = LivingArea;
                    nearArea = DiningArea;
                } else {
                    targetArea = DiningArea;
                    nearArea = LivingArea;
                }

                if (!targetArea || !nearArea) return [];
                // 目标图元有效性 - 根据category名称找到对应的家具实体
                let validTargetFurniture: TFurnitureEntity[] = [];
                targetFurnitureList.forEach(categoryName => {
                    // 在furnitureList中找到匹配的家具实体
                    let matchedFurniture = furnitureList.find(furniture => furniture.category === categoryName);
                    if (matchedFurniture) {
                        validTargetFurniture.push(matchedFurniture);
                    }
                });

                if (validTargetFurniture.length === 0) return [];

                // 一个目标图元：利用距离找edge
                if (validTargetFurniture.length == 1) {
                    let targetEdge: ZEdge = null;
                    let minDistance = Infinity;
                    // 位置：找到距离nearArea最近的edge
                    targetArea.rect.edges.forEach(edge => {
                        let distance = edge.center.distanceTo(nearArea.rect.rect_center);
                        if (distance < minDistance) {
                            minDistance = distance;
                            targetEdge = edge;
                        }
                    });
                    if (targetEdge) {
                        // 创建视角实体
                        let pos = new Vector3(targetEdge.center.x, targetEdge.center.y, targetEdge.center.z);
                        let direction = validTargetFurniture[0];
                        let target = [validTargetFurniture[0].category];
                        let viewEntity = this.createEdgeTargetView(ruler, options, pos, direction, target, roomEntity);
                        entities.push(viewEntity);
                    }
                }

                // 两个目标图元：利用两者中心点的垂线找edges,再判断是否不靠墙来选取最优
                if (validTargetFurniture.length == 2) {
                    let furniture1 = validTargetFurniture[0];
                    let furniture2 = validTargetFurniture[1];

                    // 计算两个家具中心点
                    let center1 = (furniture1.matched_rect || furniture1.rect).rect_center;
                    let center2 = (furniture2.matched_rect || furniture2.rect).rect_center;
                    let midPoint = center1.clone().add(center2).multiplyScalar(0.5);

                    // 计算两者连线的方向向量
                    let lineDirection = center2.clone().sub(center1).normalize();

                    // 计算垂直方向（法向量）
                    let perpDirection = new Vector3(-lineDirection.y, lineDirection.x, 0);

                    // 找到与垂线方向最匹配的边
                    let bestEdge: ZEdge = null;
                    let bestScore = -1;

                    targetArea.rect.edges.forEach(edge => {
                        // 计算边的方向与垂线方向的匹配度
                        let edgeDirection = edge.dv.clone().normalize();
                        let dotProduct = Math.abs(edgeDirection.dot(perpDirection));

                        // 检查这条边是否不靠墙（使用房间多边形检查）
                        let isAgainstWall = this.isEdgeAgainstWall(edge, roomEntity);

                        // 优先选择不靠墙且方向匹配的边
                        let score = dotProduct;
                        if (!isAgainstWall) {
                            score += 1.0; // 不靠墙的边得分更高
                        }

                        if (score > bestScore) {
                            bestScore = score;
                            bestEdge = edge;
                        }
                    });

                    if (bestEdge) {
                        // 在垂线与最佳边的交点或最近点创建视角
                        let edgeCenter = bestEdge.center;
                        let pos = new Vector3(edgeCenter.x, edgeCenter.y, edgeCenter.z);

                        // 方向指向两个家具的中点
                        let direction = midPoint.clone().sub(pos).normalize();
                        let target = validTargetFurniture.map(furniture => furniture.category);

                        let viewEntity = this.createEdgeTargetView(ruler, options, pos, direction, target, roomEntity);
                        entities.push(viewEntity);
                    }
                }
            }
        }
        return entities;
    }

    private createEdgeTargetView(
        ruler: ViewCameraRuler,
        options: IViewCameraGenerateOptions,
        pos: Vector3,
        direction: TFurnitureEntity | Vector3,
        target: string[],
        roomEntity: TRoomEntity
    ): TViewCameraEntity {
        let rect = new ZRect(500, 500);
        // 方向
        rect.nor = direction instanceof TFurnitureEntity
                ? (direction.matched_rect || direction.rect).nor.clone().negate()
                : direction;
        if (direction instanceof TFurnitureEntity) {
            let ToFurniture = (direction.matched_rect || direction.rect).rect_center.clone().sub(pos);
            if (rect.nor.dot(ToFurniture) < 0) {
                rect.nor = rect.nor.negate();
            }
        }
        // 位置
        rect.rect_center_3d = pos;
        let name = ruler.name + "-朝向侧方";
        let viewEntity = this.createViewCameraEntity(
            ruler,
            options,
            rect,
            target,
            roomEntity,
            name
        );
        return viewEntity;
    }

    /**
     * 检查边是否靠墙
     * @param edge 要检查的边
     * @param roomEntity 房间实体
     * @returns 是否靠墙
     */
    private isEdgeAgainstWall(edge: ZEdge, roomEntity: TRoomEntity): boolean {
        const tolerance = 100; // 容差值
        const roomPoly = roomEntity._room_poly;

        // 检查边是否与房间多边形的任何边重叠或接近
        for (let roomEdge of roomPoly.edges) {
            // 检查边是否平行且距离很近
            if (Math.abs(edge.dv.dot(roomEdge.dv)) > 0.9) {
                let distance = Math.abs(edge.projectEdge2d(roomEdge.center).y);
                if (distance < tolerance) {
                    return true;
                }
            }
        }
        return false;
    }
}
