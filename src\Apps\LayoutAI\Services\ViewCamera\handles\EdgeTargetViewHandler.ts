import { BaseViewHandler } from "./BaseViewHandler";
import { ViewCameraRuler } from "../ViewCameraRuler";
import {
    TViewCameraEntity,
    IViewCameraGenerateOptions
} from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { RoomSpaceAreaType, IType2UITypeDict } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { ZEdge, ZRect } from "@layoutai/z_polygon";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { Vector3 } from "three";
import { TBaseRoomToolUtil } from "@/Apps/LayoutAI/Layout/TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";

/**
 * 根据房间中某个图元生成视角
 * 位置：分区的边中点
 */
export class EdgeTargetViewHandler extends BaseViewHandler {
    handle(
        ruler: ViewCameraRuler,
        roomEntity: TRoomEntity,
        options: IViewCameraGenerateOptions
    ): TViewCameraEntity[] {
        let entities: TViewCameraEntity[] = [];
        if (this.checkCondition(ruler, roomEntity)) {
            const targetFurnitureList = ruler.targets;
            const furnitureList = roomEntity.getFurnitureEntitiesOnFlat();
            // 找客餐厅分区
            const areas: TSubSpaceAreaEntity[] = roomEntity._sub_room_areas;
            let LivingArea: TSubSpaceAreaEntity = null; // 客厅
            let DiningArea: TSubSpaceAreaEntity = null; // 餐厅
            areas.forEach(area => {
                if (area.space_area_type === RoomSpaceAreaType.LivingArea) {
                    LivingArea = area;
                } else if (area.space_area_type === RoomSpaceAreaType.DiningArea) {
                    DiningArea = area;
                }
            });
            if (ruler.condition?.spaceArea) {
                let targetArea: TSubSpaceAreaEntity = null;
                let nearArea: TSubSpaceAreaEntity = null;
                if (ruler.condition.spaceArea === IType2UITypeDict[RoomSpaceAreaType.LivingArea]) {
                    targetArea = LivingArea;
                    nearArea = DiningArea;
                } else {
                    targetArea = DiningArea;
                    nearArea = LivingArea;
                }
                if (!targetArea || !nearArea) return [];

                // 目标图元有效性
                let validTargetFurniture: TFurnitureEntity[] = [];
                targetFurnitureList.forEach(targetFurniture => {
                    let matchedFurniture = furnitureList.find(furniture => furniture.category.endsWith(targetFurniture as string));
                    if (matchedFurniture) {
                        validTargetFurniture.push(matchedFurniture);
                    }
                });

                // 一个目标图元：利用最小距离确定edge
                if (validTargetFurniture.length == 1) {
                    let targetEdge: ZEdge = null;
                    let minDistance = Infinity;
                    targetArea.rect.edges.forEach(edge => {
                        const distance = edge.center.distanceTo(nearArea.rect.rect_center);
                        if (distance < minDistance) {
                            minDistance = distance;
                            targetEdge = edge;
                        }
                    });
                    if (targetEdge) {
                        const pos = targetEdge.center.clone();
                        const direction = validTargetFurniture[0];
                        const target = [validTargetFurniture[0].category];
                        const viewEntity = this.createEdgeTargetView(ruler, options, pos, direction, target, roomEntity);
                        entities.push(viewEntity);
                    }
                }

                // 两个目标图元：利用两者中心点的垂线找edges,再判断是否不靠墙来选取最优
                if (validTargetFurniture.length == 2) {
                    const [furniture1, furniture2] = validTargetFurniture;
                    const center1 = (furniture1.matched_rect || furniture1.rect).rect_center;
                    const center2 = (furniture2.matched_rect || furniture2.rect).rect_center;
                    const midPoint = center1.clone().add(center2).multiplyScalar(0.5);

                    // 计算垂直方向
                    const lineDirection = center2.clone().sub(center1).normalize();
                    const perpDirection = new Vector3(-lineDirection.y, lineDirection.x, 0);

                    // 找到最佳边
                    let bestEdge: ZEdge = null;
                    for (let edge of targetArea.rect.edges) {
                        // 检查边是否靠墙
                        const isChecktWall = TBaseRoomToolUtil.instance.edgeOnPolygon(edge, roomEntity._room_poly) === null;
                        console.log(isChecktWall, edge);
                        if (isChecktWall) {
                            bestEdge = edge;
                            break;
                        }
                    }
                    if (bestEdge) {
                        // 使用射线与边的交点计算
                        const ray_start = targetArea.rect.rect_center.clone();
                        const ray_direction = perpDirection.clone();
                        const intersection = targetArea.rect.getRayIntersection(ray_start, ray_direction);
                        let pos: Vector3 = null;
                        if (intersection && intersection.point) {
                            pos = intersection.point;
                        } else {
                            pos = bestEdge.center.clone();
                        }
                        if (!pos) return;
                        const direction = midPoint.clone().sub(pos).normalize();
                        const target = validTargetFurniture.map(furniture => furniture.category);
                        const viewEntity = this.createEdgeTargetView(ruler, options, pos, direction, target, roomEntity);
                        entities.push(viewEntity);
                    }
                }
            }
        }
        return entities;
    }

    private createEdgeTargetView(
        ruler: ViewCameraRuler,
        options: IViewCameraGenerateOptions,
        pos: Vector3,
        direction: TFurnitureEntity | Vector3,
        target: string[],
        roomEntity: TRoomEntity
    ): TViewCameraEntity {
        let rect = new ZRect(500, 500);
        // 方向
        rect.nor = direction instanceof TFurnitureEntity
                ? (direction.matched_rect || direction.rect).nor.clone().negate()
                : direction;
        if (direction instanceof TFurnitureEntity) {
            let ToFurniture = (direction.matched_rect || direction.rect).rect_center.clone().sub(pos);
            if (rect.nor.dot(ToFurniture) < 0) {
                rect.nor = rect.nor.negate();
            }
        }
        // 位置
        rect.rect_center_3d = this.getViewCameraPosByPoint(ruler, pos, rect.nor);
        let name = ruler.name + "-侧方";
        let viewEntity = this.createViewCameraEntity(
            ruler,
            options,
            rect,
            target,
            roomEntity,
            name
        );
        return viewEntity;
    }
}
