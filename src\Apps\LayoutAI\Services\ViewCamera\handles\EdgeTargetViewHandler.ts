import { BaseViewHandler } from "./BaseViewHandler";
import { ViewCameraRuler } from "../ViewCameraRuler";
import {
    TViewCameraEntity,
    IViewCameraGenerateOptions
} from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { RoomSpaceAreaType, IType2UITypeDict } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { ZEdge, ZRect } from "@layoutai/z_polygon";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { Vector3 } from "three";

/**
 * 根据房间中某个图元生成视角
 * 位置：分区的边中点
 */
export class EdgeTargetViewHandler extends BaseViewHandler {
    handle(
        ruler: ViewCameraRuler,
        roomEntity: TRoomEntity,
        options: IViewCameraGenerateOptions
    ): TViewCameraEntity[] {
        let entities: TViewCameraEntity[] = [];
        if (this.checkCondition(ruler, roomEntity)) {
            let targetFurnitureList = ruler.targets;
            let furnitureList = roomEntity.getFurnitureEntitiesOnFlat();
            // 找客餐厅分区
            let areas: TSubSpaceAreaEntity[] = roomEntity._sub_room_areas;
            let LivingArea: TSubSpaceAreaEntity = null; // 客厅
            let DiningArea: TSubSpaceAreaEntity = null; // 餐厅
            areas.forEach(area => {
                if (area.space_area_type === RoomSpaceAreaType.LivingArea) {
                    LivingArea = area;
                } else if (area.space_area_type === RoomSpaceAreaType.DiningArea) {
                    DiningArea = area;
                }
            });
            if (ruler.condition?.spaceArea) {
                let targetArea: TSubSpaceAreaEntity = null;
                let nearArea: TSubSpaceAreaEntity = null;
                if (ruler.condition.spaceArea === IType2UITypeDict[RoomSpaceAreaType.LivingArea]) {
                    targetArea = LivingArea;
                    nearArea = DiningArea;
                } else {
                    targetArea = DiningArea;
                    nearArea = LivingArea;
                }

                if (!targetArea || !nearArea) return [];
                // 目标图元有效性 - 根据category名称找到对应的家具实体
                let validTargetFurniture: TFurnitureEntity[] = [];
                targetFurnitureList.forEach(categoryName => {
                    // 在furnitureList中找到匹配的家具实体
                    let matchedFurniture = furnitureList.find(furniture => furniture.category === categoryName);
                    if (matchedFurniture) {
                        validTargetFurniture.push(matchedFurniture);
                    }
                });

                if (validTargetFurniture.length === 0) return [];

                // 一个目标图元：利用距离找edge
                if (validTargetFurniture.length == 1) {
                    const targetEdge = this.findNearestEdge(targetArea.rect.edges, nearArea.rect.rect_center);
                    if (targetEdge) {
                        const pos = targetEdge.center.clone();
                        const direction = validTargetFurniture[0];
                        const target = [validTargetFurniture[0].category];
                        const viewEntity = this.createEdgeTargetView(ruler, options, pos, direction, target, roomEntity);
                        entities.push(viewEntity);
                    }
                }

                // 两个目标图元：利用两者中心点的垂线找edges,再判断是否不靠墙来选取最优
                if (validTargetFurniture.length == 2) {
                    const [furniture1, furniture2] = validTargetFurniture;
                    const center1 = (furniture1.matched_rect || furniture1.rect).rect_center;
                    const center2 = (furniture2.matched_rect || furniture2.rect).rect_center;
                    const midPoint = center1.clone().add(center2).multiplyScalar(0.5);

                    // 计算垂直方向
                    const lineDirection = center2.clone().sub(center1).normalize();
                    const perpDirection = new Vector3(-lineDirection.y, lineDirection.x, 0);

                    // 找到最佳边
                    const bestEdge = this.findBestEdgeForTwoTargets(targetArea.rect.edges, perpDirection, roomEntity);

                    if (bestEdge) {
                        // 计算垂线与边的交点
                        const pos = this.calculatePerpendicularIntersection(midPoint, perpDirection, bestEdge);
                        const direction = midPoint.clone().sub(pos).normalize();
                        const target = validTargetFurniture.map(furniture => furniture.category);

                        const viewEntity = this.createEdgeTargetView(ruler, options, pos, direction, target, roomEntity);
                        entities.push(viewEntity);
                    }
                }
            }
        }
        return entities;
    }

    private createEdgeTargetView(
        ruler: ViewCameraRuler,
        options: IViewCameraGenerateOptions,
        pos: Vector3,
        direction: TFurnitureEntity | Vector3,
        target: string[],
        roomEntity: TRoomEntity
    ): TViewCameraEntity {
        let rect = new ZRect(500, 500);
        // 方向
        rect.nor = direction instanceof TFurnitureEntity
                ? (direction.matched_rect || direction.rect).nor.clone().negate()
                : direction;
        if (direction instanceof TFurnitureEntity) {
            let ToFurniture = (direction.matched_rect || direction.rect).rect_center.clone().sub(pos);
            if (rect.nor.dot(ToFurniture) < 0) {
                rect.nor = rect.nor.negate();
            }
        }
        // 位置
        rect.rect_center_3d = pos;
        let name = ruler.name + "-朝向侧方";
        let viewEntity = this.createViewCameraEntity(
            ruler,
            options,
            rect,
            target,
            roomEntity,
            name
        );
        return viewEntity;
    }

    /**
     * 为两个目标图元找到最佳边
     */
    private findBestEdgeForTwoTargets(edges: ZEdge[], perpDirection: Vector3, roomEntity: TRoomEntity): ZEdge {
        let bestEdge: ZEdge = null;
        let bestScore = -1;

        edges.forEach(edge => {
            const edgeDirection = edge.dv.clone().normalize();
            const dotProduct = Math.abs(edgeDirection.dot(perpDirection));
            const isAgainstWall = this.isEdgeAgainstWall(edge, roomEntity);

            const score = dotProduct + (isAgainstWall ? 0 : 1.0);
            if (score > bestScore) {
                bestScore = score;
                bestEdge = edge;
            }
        });

        return bestEdge;
    }

    /**
     * 计算垂线与边的交点
     */
    private calculatePerpendicularIntersection(point: Vector3, perpDirection: Vector3, edge: ZEdge): Vector3 {
        // 创建从midPoint出发的垂线
        const perpLine = new ZEdge(
            { pos: point },
            { pos: point.clone().add(perpDirection.clone().multiplyScalar(10000)) }
        );
        perpLine.computeNormal();

        // 计算交点
        const intersection = perpLine.getIntersection(edge);
        if (intersection) {
            // 检查交点是否在边的范围内
            const projectedPoint = edge.projectEdge2d(intersection);
            if (projectedPoint.x >= 0 && projectedPoint.x <= edge.length) {
                return intersection;
            }
        }

        // 如果没有有效交点，返回边的中心点
        return edge.center.clone();
    }

    /**
     * 检查边是否靠墙
     */
    private isEdgeAgainstWall(edge: ZEdge, roomEntity: TRoomEntity): boolean {
        const tolerance = 100;
        const roomPoly = roomEntity._room_poly;

        return roomPoly.edges.some(roomEdge => {
            if (Math.abs(edge.dv.dot(roomEdge.dv)) > 0.9) {
                const distance = Math.abs(edge.projectEdge2d(roomEdge.center).y);
                return distance < tolerance;
            }
            return false;
        });
    }
}
